# 📝 برنامه یادداشت PHP

یک برنامه ساده و کاربردی برای مدیریت یادداشت‌ها با PHP که شامل قابلیت‌های ایجاد، ویرایش، حذف و مشاهده یادداشت‌ها می‌باشد.

## ویژگی‌ها

- ✅ ایجاد یادداشت جدید
- ✅ مشاهده تمام یادداشت‌ها
- ✅ ویرایش یادداشت‌های موجود
- ✅ حذف یادداشت‌ها
- ✅ رابط کاربری زیبا و ریسپانسیو
- ✅ پشتیبانی از زبان فارسی

## نحوه نصب و راه‌اندازی

### پیش‌نیازها
- XAMPP نصب شده
- مرورگر وب

### مراحل نصب

1. **راه‌اندازی XAMPP:**
   - XAMPP را باز کنید
   - Apache و MySQL را استارت کنید

2. **کپی فایل‌ها:**
   - تمام فایل‌های پروژه را در پوشه `htdocs/notes` کپی کنید
   - مسیر کامل: `C:\xampp\htdocs\notes\`

3. **ایجاد دیتابیس:**
   - به آدرس `http://localhost/phpmyadmin` بروید
   - فایل `database.sql` را باز کنید و محتوای آن را در phpMyAdmin اجرا کنید
   - یا به صورت دستی:
     - دیتابیس جدیدی با نام `notes_app` ایجاد کنید
     - جدول `notes` را با ساختار موجود در فایل SQL ایجاد کنید

4. **اجرای برنامه:**
   - به آدرس `http://localhost/notes` بروید
   - برنامه آماده استفاده است!

## ساختار فایل‌ها

```
project.php/
├── index.php          # صفحه اصلی - نمایش تمام یادداشت‌ها
├── add_note.php       # صفحه افزودن یادداشت جدید
├── edit_note.php      # صفحه ویرایش یادداشت
├── delete_note.php    # حذف یادداشت
├── config.php         # تنظیمات اتصال به دیتابیس
├── style.css          # فایل استایل
├── database.sql       # ساختار دیتابیس
└── README.md          # راهنمای استفاده
```

## تنظیمات دیتابیس

اگر نیاز به تغییر تنظیمات دیتابیس دارید، فایل `config.php` را ویرایش کنید:

```php
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'notes_app';
```

## استفاده از برنامه

1. **مشاهده یادداشت‌ها:** صفحه اصلی تمام یادداشت‌ها را نمایش می‌دهد
2. **افزودن یادداشت:** روی دکمه "یادداشت جدید" کلیک کنید
3. **ویرایش یادداشت:** روی دکمه "ویرایش" در هر یادداشت کلیک کنید
4. **حذف یادداشت:** روی دکمه "حذف" کلیک کنید (با تأیید)

## مشکلات رایج

- **خطای اتصال به دیتابیس:** مطمئن شوید MySQL در XAMPP فعال است
- **صفحه خالی:** بررسی کنید که فایل‌ها در مسیر صحیح قرار دارند
- **خطای 404:** مطمئن شوید Apache در XAMPP فعال است

## توسعه بیشتر

می‌توانید ویژگی‌های زیر را اضافه کنید:
- سیستم جستجو
- دسته‌بندی یادداشت‌ها
- سیستم کاربری
- بکاپ گیری از یادداشت‌ها

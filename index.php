<?php
require_once 'config.php';

// Fetch all notes
$stmt = $pdo->query("SELECT * FROM notes ORDER BY updated_at DESC");
$notes = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>یادداشت‌های من</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>📝 یادداشت‌های من</h1>
            <a href="add_note.php" class="btn btn-primary">+ یادداشت جدید</a>
        </header>

        <main>
            <?php if (empty($notes)): ?>
                <div class="empty-state">
                    <p>هیچ یادداشتی وجود ندارد.</p>
                    <a href="add_note.php" class="btn btn-primary">اولین یادداشت خود را بسازید</a>
                </div>
            <?php else: ?>
                <div class="notes-grid">
                    <?php foreach ($notes as $note): ?>
                        <div class="note-card">
                            <div class="note-header">
                                <h3><?php echo htmlspecialchars($note['title']); ?></h3>
                                <div class="note-actions">
                                    <a href="edit_note.php?id=<?php echo $note['id']; ?>" class="btn btn-small btn-secondary">ویرایش</a>
                                    <a href="delete_note.php?id=<?php echo $note['id']; ?>" 
                                       class="btn btn-small btn-danger" 
                                       onclick="return confirm('آیا مطمئن هستید که می‌خواهید این یادداشت را حذف کنید؟')">حذف</a>
                                </div>
                            </div>
                            <div class="note-content">
                                <p><?php echo nl2br(htmlspecialchars(substr($note['content'], 0, 200))); ?>
                                <?php if (strlen($note['content']) > 200): ?>...<?php endif; ?></p>
                            </div>
                            <div class="note-footer">
                                <small>آخرین بروزرسانی: <?php echo date('Y/m/d H:i', strtotime($note['updated_at'])); ?></small>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </main>
    </div>
</body>
</html>

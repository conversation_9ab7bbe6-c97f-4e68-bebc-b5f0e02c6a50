<?php
// Database setup script
$host = 'localhost';
$username = 'root';
$password = '';

try {
    // Connect to MySQL server (without specifying database)
    $pdo = new PDO("mysql:host=$host;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ اتصال به MySQL موفق بود<br>";
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS notes_app");
    echo "✅ دیتابیس notes_app ایجاد شد<br>";
    
    // Use the database
    $pdo->exec("USE notes_app");
    
    // Create table
    $sql = "CREATE TABLE IF NOT EXISTS notes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "✅ جدول notes ایجاد شد<br>";
    
    // Insert sample data
    $stmt = $pdo->prepare("INSERT INTO notes (title, content) VALUES (?, ?)");
    
    // Check if sample data already exists
    $check = $pdo->query("SELECT COUNT(*) FROM notes")->fetchColumn();
    
    if ($check == 0) {
        $stmt->execute(['یادداشت خوش‌آمدگویی', 'به برنامه یادداشت‌های من خوش آمدید! شما می‌توانید یادداشت‌های خود را ایجاد، ویرایش و حذف کنید.']);
        $stmt->execute(['یادداشت نمونه', 'این یک یادداشت نمونه است تا عملکرد برنامه را نشان دهد. می‌توانید آن را ویرایش یا حذف کنید.']);
        echo "✅ داده‌های نمونه اضافه شدند<br>";
    } else {
        echo "ℹ️ داده‌های نمونه از قبل موجود هستند<br>";
    }
    
    echo "<br><strong>🎉 راه‌اندازی کامل شد!</strong><br>";
    echo "<a href='http://localhost/notes/' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 10px; display: inline-block;'>🚀 مشاهده برنامه</a>";
    
} catch(PDOException $e) {
    echo "❌ خطا: " . $e->getMessage();
}
?>

<?php
require_once 'config.php';

$message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = trim($_POST['title']);
    $content = trim($_POST['content']);
    
    if (!empty($title) && !empty($content)) {
        try {
            $stmt = $pdo->prepare("INSERT INTO notes (title, content) VALUES (?, ?)");
            $stmt->execute([$title, $content]);
            header('Location: index.php');
            exit;
        } catch(PDOException $e) {
            $message = 'خطا در ذخیره یادداشت: ' . $e->getMessage();
        }
    } else {
        $message = 'لطفاً تمام فیلدها را پر کنید.';
    }
}
?>

<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>یادداشت جدید</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>📝 یادداشت جدید</h1>
            <a href="index.php" class="btn btn-secondary">← بازگشت</a>
        </header>

        <main>
            <?php if ($message): ?>
                <div class="alert alert-error"><?php echo $message; ?></div>
            <?php endif; ?>

            <form method="POST" class="note-form">
                <div class="form-group">
                    <label for="title">عنوان یادداشت:</label>
                    <input type="text" id="title" name="title" required 
                           value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>">
                </div>

                <div class="form-group">
                    <label for="content">محتوای یادداشت:</label>
                    <textarea id="content" name="content" rows="10" required><?php echo isset($_POST['content']) ? htmlspecialchars($_POST['content']) : ''; ?></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">💾 ذخیره یادداشت</button>
                    <a href="index.php" class="btn btn-secondary">انصراف</a>
                </div>
            </form>
        </main>
    </div>
</body>
</html>

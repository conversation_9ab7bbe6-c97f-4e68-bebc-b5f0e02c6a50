<?php
require_once 'config.php';

// Get note ID from URL
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id <= 0) {
    header('Location: index.php');
    exit;
}

try {
    // Check if note exists
    $stmt = $pdo->prepare("SELECT id FROM notes WHERE id = ?");
    $stmt->execute([$id]);
    
    if ($stmt->fetch()) {
        // Delete the note
        $stmt = $pdo->prepare("DELETE FROM notes WHERE id = ?");
        $stmt->execute([$id]);
    }
    
    header('Location: index.php');
    exit;
} catch(PDOException $e) {
    // Redirect back with error (you could add session flash messages here)
    header('Location: index.php');
    exit;
}
?>

<?php
require_once 'config.php';

$message = '';
$note = null;

// Get note ID from URL
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id <= 0) {
    header('Location: index.php');
    exit;
}

// Fetch the note
try {
    $stmt = $pdo->prepare("SELECT * FROM notes WHERE id = ?");
    $stmt->execute([$id]);
    $note = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$note) {
        header('Location: index.php');
        exit;
    }
} catch(PDOException $e) {
    $message = 'خطا در بارگذاری یادداشت: ' . $e->getMessage();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = trim($_POST['title']);
    $content = trim($_POST['content']);
    
    if (!empty($title) && !empty($content)) {
        try {
            $stmt = $pdo->prepare("UPDATE notes SET title = ?, content = ? WHERE id = ?");
            $stmt->execute([$title, $content, $id]);
            header('Location: index.php');
            exit;
        } catch(PDOException $e) {
            $message = 'خطا در بروزرسانی یادداشت: ' . $e->getMessage();
        }
    } else {
        $message = 'لطفاً تمام فیلدها را پر کنید.';
    }
}
?>

<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ویرایش یادداشت</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>✏️ ویرایش یادداشت</h1>
            <a href="index.php" class="btn btn-secondary">← بازگشت</a>
        </header>

        <main>
            <?php if ($message): ?>
                <div class="alert alert-error"><?php echo $message; ?></div>
            <?php endif; ?>

            <?php if ($note): ?>
                <form method="POST" class="note-form">
                    <div class="form-group">
                        <label for="title">عنوان یادداشت:</label>
                        <input type="text" id="title" name="title" required 
                               value="<?php echo htmlspecialchars(isset($_POST['title']) ? $_POST['title'] : $note['title']); ?>">
                    </div>

                    <div class="form-group">
                        <label for="content">محتوای یادداشت:</label>
                        <textarea id="content" name="content" rows="10" required><?php echo htmlspecialchars(isset($_POST['content']) ? $_POST['content'] : $note['content']); ?></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">💾 بروزرسانی یادداشت</button>
                        <a href="index.php" class="btn btn-secondary">انصراف</a>
                    </div>
                </form>
            <?php endif; ?>
        </main>
    </div>
</body>
</html>
